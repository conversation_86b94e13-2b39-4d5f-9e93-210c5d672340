<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Traits\LivewireGeneralFunctions;
use Illuminate\Support\Facades\RateLimiter;
use <PERSON>loudemans\Shoppingcart\Facades\Cart;

class WonTopic extends Component
{
   use LivewireGeneralFunctions;
   
    public $userDetails;
    public $topicText = '';
    public $frequency = 0;
    public $timeDisplay = '';
    public $timeSeconds = 0;
    public $showFrequency = false;
    public $showTime = false;
    public $isCalculating = false;
    public $farbklang = false;

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
    ];

    public function mount()
    {
        $this->userDetails = getUserDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';
        // Initialize frequency and time if topic exists
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    public function updatedTopicText()
    {
        $this->calculateFrequencyIfNeeded();
    }

    public function calculateFrequencyIfNeeded()
    {
        // Only update frequency if text is not empty and has meaningful content
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $this->updateFrequencyFromTopic();
        } else {
            // Reset frequency display for empty or very short text
            $this->frequency = 0;
            $this->timeSeconds = 0;
            $this->timeDisplay = '';
            $this->showFrequency = false;
            $this->showTime = false;
        }
    }

    protected function updateFrequencyFromTopic()
    {
        // Don't recalculate if already calculating
        if ($this->isCalculating) {
            return;
        }

        $this->isCalculating = true;

        try {
            // Calculate frequency using the helper function
            $this->frequency = $this->calculateFrequencySafely($this->topicText);

            if ($this->frequency > 0) {
                // Generate random time based on biorhythm details
                $biorythDetails = biorythVisibleDetails();
                $this->timeSeconds = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                $this->timeDisplay = gmdate('i:s', $this->timeSeconds);
                $this->showFrequency = true;
                $this->showTime = true;
            } else {
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
            }
        } catch (\Exception $e) {
            Log::error('Topic frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
            // Don't show error toastr during typing, just log it
        } finally {
            $this->isCalculating = false;
        }
    }

    protected function calculateFrequencySafely(string $topic): int
    {
        try {
            // Quick cache for recent calculations (5 minutes)
            $cacheKey = 'freq_calc_' . md5(trim($topic));

            return Cache::remember($cacheKey, 300, function () use ($topic) {
                $frequency = calculationFrequency(trim($topic));

                // Apply transformation logic from CalculationController
                if ($frequency > 0 && $frequency < 120) {
                    $frequency *= 3;
                } else if ($frequency > 120 && $frequency < 240) {
                    $frequency *= 2;
                }
                if ($frequency < 250) {
                    $frequency = 250;
                }

                // Validate frequency is within acceptable range
                if ($frequency < 250 || $frequency > 20000) {
                    return 250; // Default to minimum valid frequency
                }

                return (int)$frequency;
            });
        } catch (\Exception $e) {
            Log::error('Frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($topic, 0, 50) . '...'
            ]);
            return 250; // Return default frequency on error
        }
    }

    public function addToCart()
    {
        if (empty(trim($this->topicText))) {
            $this->showToastr('warning', trans('action.warning'), trans('action.no_product_title'));
            return;
        }

        try {
            // Simple rate limiting check
            $key = 'cart_add_' . getUserId();
            if (RateLimiter::tooManyAttempts($key, 10)) {
                $this->showToastr('warning', trans('action.warning'), trans('action.cart_max_allow_alert'));
                return;
            }

            RateLimiter::hit($key, 60);

            // Ensure we have frequency calculated
            if ($this->frequency <= 0) {
                $this->frequency = $this->calculateFrequencySafely($this->topicText);
            }

            // Ensure we have time calculated
            if ($this->timeSeconds <= 0) {
                $biorythDetails = biorythVisibleDetails();
                if ($biorythDetails) {
                    $this->timeSeconds = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                } else {
                    // Fallback if biorythDetails is null
                    $this->timeSeconds = rand(60, 300);
                }
            }

            // Create a request object with the required data for SajaxController
            $requestData = [
                'ana_id'      => 1,
                'name'        => trim($this->topicText),
                'submenu_id'  => '',
                'proID'       => '',
                'calculation' => '',
                'male'        => '',
                'heart'       => '',
                'price'       => (int)$this->timeSeconds,
                'causes_id'   => '',
                'medium_id'   => '',
                'tipp_id'     => '',
                'color'       => '',
                'type'        => 'Topic',
                'frequency'   => (int)$this->frequency,
                'time'        => (int)$this->timeSeconds
            ];

            // Use SajaxController's add2Cart method by calling it directly
            $response = $this->callSajaxAdd2Cart($requestData);

            $responseData = $response;

            if ($responseData['success']) {
                $this->showToastr('success', trans('action.success'), trans('action.added_successfully'));
                $this->dispatch('cartUpdated');

                Log::info('Topic added to cart successfully via SajaxController', [
                    'user_id' => getUserId(),
                    'topic' => substr($this->topicText, 0, 50) . '...',
                    'frequency' => $this->frequency,
                    'time' => $this->timeSeconds,
                    'cart_id' => $responseData['cart_id']
                ]);
            } else {
                $this->showToastr('error', trans('action.error'), $responseData['message'] ?? trans('action.cart_add_failed'));
            }

        } catch (\Exception $e) {
            Log::error('Cart addition failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...',
                'trace' => $e->getTraceAsString()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.cart_add_failed'));
        }
    }

    /**
     * Call SajaxController's add2Cart method with the provided data
     * This method replicates the logic from SajaxController::add2Cart
     * to avoid request manipulation issues
     */
    protected function callSajaxAdd2Cart(array $data): array
    {
        try {
            // Check user access (simplified version)
            if (!getUserId()) {
                return [
                    'success' => false,
                    '_alert_type' => 'warning',
                    '_alert' => trans('action.warning'),
                    'message' => trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
                ];
            }

            // Check cart count limit
            if (getCartCount() > 199) {
                return [
                    'success' => false,
                    '_alert_type' => 'warning',
                    '_alert' => trans('action.warning'),
                    'message' => trans('action.cart_max_allow_alert')
                ];
            }

            // Handle price calculation for Topic type
            if ($data['type'] === 'Topic' && isset($data['time']) && isset($data['frequency'])) {
                $price = $data['time'];
            } else {
                $price = $data['price'];
            }

            // Prepare cart data similar to SajaxController
            $cartData = [
                'userID' => getUserId(),
                'analysisID' => $data['ana_id'] ?? 0,
                'analysisName' => isset($data['frequency']) ? $data['name'] . ' ( ' . $data['frequency'] . ' Hz )' : $data['name'],
                'submenu_id' => $data['submenu_id'] ?? 0,
                'productID' => $data['proID'] ?? '',
                'calculation' => $data['calculation'] ?? 0,
                'male' => $data['male'] ?? 0,
                'heart' => $data['heart'] ?? 0,
                'price' => $price,
                'causes_id' => $data['causes_id'] ?? 0,
                'medium_id' => $data['medium_id'] ?? 0,
                'tipp_id' => $data['tipp_id'] ?? 0,
                'color' => $data['color'] ?? null,
                'type' => $data['type'],
                'others' => []
            ];

            // Add to cart using Laravel Shopping Cart
            $insertdata = Cart::instance('wishlist')->add([
                'id' => getUserId(),
                'name' => $data['type'],
                'qty' => 1,
                'price' => 1, // Using 1 as per SajaxController logic
                'weight' => 1,
                'options' => $cartData
            ]);

            $getData = Cart::get($insertdata->rowId);

            return [
                'success' => true,
                'cart_id' => $insertdata->rowId,
                'cart_data' => $getData->options->toArray(),
                'result' => gmdate('i:s', $cartData['price'])
            ];

        } catch (\Exception $e) {
            Log::error('SajaxController add2Cart simulation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'success' => false,
                '_alert_type' => 'error',
                '_alert' => trans('action.error'),
                'message' => trans('action.cart_add_failed')
            ];
        }
    }

    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);

            if ($updated) {
                $this->frequency = 0;
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }

    protected function showToastr($type, $title, $message)
    {
        $this->dispatch('show-toastr', [
            'type' => $type,
            'title' => $title,
            'message' => $message
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.won-topic');
    }
}
