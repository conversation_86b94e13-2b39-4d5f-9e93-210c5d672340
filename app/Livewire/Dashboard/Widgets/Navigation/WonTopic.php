<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Traits\LivewireGeneralFunctions;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Support\Facades\RateLimiter;

class WonTopic extends Component
{
   use LivewireGeneralFunctions;
   
    public $userDetails;
    public $topicText = '';
    public $frequency = 0;
    public $timeDisplay = '';
    public $timeSeconds = 0;
    public $showFrequency = false;
    public $showTime = false;
    public $isCalculating = false;
    public $farbklang = false;

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
    ];

    public function mount()
    {
        $this->userDetails = getUserDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';
        // Initialize frequency and time if topic exists
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    public function updatedTopicText()
    {
        $this->calculateFrequencyIfNeeded();
    }

    public function calculateFrequencyIfNeeded()
    {
        // Only update frequency if text is not empty and has meaningful content
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $this->updateFrequencyFromTopic();
        } else {
            // Reset frequency display for empty or very short text
            $this->frequency = 0;
            $this->timeSeconds = 0;
            $this->timeDisplay = '';
            $this->showFrequency = false;
            $this->showTime = false;
        }
    }

    protected function updateFrequencyFromTopic()
    {
        // Don't recalculate if already calculating
        if ($this->isCalculating) {
            return;
        }

        $this->isCalculating = true;

        try {
            // Calculate frequency using the helper function
            $this->frequency = $this->calculateFrequencySafely($this->topicText);

            if ($this->frequency > 0) {
                // Generate random time based on biorhythm details
                $biorythDetails = biorythVisibleDetails();
                $this->timeSeconds = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                $this->timeDisplay = gmdate('i:s', $this->timeSeconds);
                $this->showFrequency = true;
                $this->showTime = true;
            } else {
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
            }
        } catch (\Exception $e) {
            Log::error('Topic frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
            // Don't show error toastr during typing, just log it
        } finally {
            $this->isCalculating = false;
        }
    }

    protected function calculateFrequencySafely(string $topic): int
    {
        try {
            // Quick cache for recent calculations (5 minutes)
            $cacheKey = 'freq_calc_' . md5(trim($topic));

            return Cache::remember($cacheKey, 300, function () use ($topic) {
                $frequency = calculationFrequency(trim($topic));

                // Apply transformation logic from CalculationController
                if ($frequency > 0 && $frequency < 120) {
                    $frequency *= 3;
                } else if ($frequency > 120 && $frequency < 240) {
                    $frequency *= 2;
                }
                if ($frequency < 250) {
                    $frequency = 250;
                }

                // Validate frequency is within acceptable range
                if ($frequency < 250 || $frequency > 20000) {
                    return 250; // Default to minimum valid frequency
                }

                return (int)$frequency;
            });
        } catch (\Exception $e) {
            Log::error('Frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($topic, 0, 50) . '...'
            ]);
            return 250; // Return default frequency on error
        }
    }

    public function addToCart()
    {
        if (empty(trim($this->topicText))) {
            $this->showToastr('warning', trans('action.warning'), trans('action.no_product_title'));
            return;
        }

        try {
            // Simple rate limiting check
            $key = 'cart_add_' . getUserId();
            if (RateLimiter::tooManyAttempts($key, 10)) {
                $this->showToastr('warning', trans('action.warning'), trans('action.cart_max_allow_alert'));
                return;
            }

            RateLimiter::hit($key, 60);

            // Ensure we have frequency calculated
            if ($this->frequency <= 0) {
                $this->frequency = $this->calculateFrequencySafely($this->topicText);
            }

            // Ensure we have time calculated
            if ($this->timeSeconds <= 0) {
                $biorythDetails = biorythVisibleDetails();
                if ($biorythDetails) {
                    $this->timeSeconds = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                } else {
                    // Fallback if biorythDetails is null
                    $this->timeSeconds = rand(60, 300);
                }
            }

            // Prepare cart data for direct cart addition (bypass HTTP request)
            $cartData = [
                'user_id' => getUserId(),
                'analysisID' => 1,
                'analysisName' => trim($this->topicText),
                'analysisTitle' => trim($this->topicText),
                'submenu_id' => 0,
                'productID' => 0,
                'calculation' => 0,
                'male' => '',
                'heart' => '',
                'price' => (int)$this->timeSeconds,
                'causes_id' => 0,
                'medium_id' => 0,
                'tipp_id' => 0,
                'color' => '',
                'type' => 'Topic',
                'percentage' => 0,
                'others' => [],
                'op_status' => true,
                'frequency' => (int)$this->frequency,
                'time' => (int)$this->timeSeconds
            ];

            // Add directly to cart using Laravel Shopping Cart
            $insertdata = Cart::instance('wishlist')->add([
                'id' => getUserId(),
                'name' => 'Topic',
                'qty' => 1,
                'price' => (int)$this->timeSeconds,
                'weight' => 1,
                'options' => $cartData
            ]);

            if ($insertdata) {
                $this->showToastr('success', trans('action.success'), trans('action.added_successfully'));
                $this->dispatch('cartUpdated');

                Log::info('Topic added to cart successfully', [
                    'user_id' => getUserId(),
                    'topic' => substr($this->topicText, 0, 50) . '...',
                    'frequency' => $this->frequency,
                    'time' => $this->timeSeconds,
                    'cart_id' => $insertdata->rowId
                ]);
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.cart_add_failed'));
            }

        } catch (\Exception $e) {
            Log::error('Cart addition failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...',
                'trace' => $e->getTraceAsString()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.cart_add_failed'));
        }
    }

    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);

            if ($updated) {
                $this->frequency = 0;
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }

    protected function showToastr($type, $title, $message)
    {
        $this->dispatch('show-toastr', [
            'type' => $type,
            'title' => $title,
            'message' => $message
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.won-topic');
    }
}
